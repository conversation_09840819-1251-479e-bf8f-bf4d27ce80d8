import streamlit as st
import json
import requests

st.set_page_config(page_title="Doctor-Patient Conversation Analyzer", layout="centered")
st.title("🏥 Doctor-Patient Conversation Analyzer")
st.write("""
- Click the mic button to start recording.
- Click the mic button again to stop and analyze the conversation.
- The transcript and chat timeline will be shown automatically after analysis.
""")

# Embed the HTML/JS frontend for manual recording control
html_code = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor-Patient Conversation Analyzer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; }
        #micBtn { font-size: 2rem; padding: 20px; border-radius: 50%; border: none; background: #4CAF50; color: white; cursor: pointer; }
        #micBtn.recording { background: #f44336; }
        #result { margin-top: 30px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <button id="micBtn">🎤 Start</button>
    <div id="status"></div>
    <div id="result"></div>
    <script>
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        let stream = null;
        const micBtn = document.getElementById('micBtn');
        const statusDiv = document.getElementById('status');
        const resultDiv = document.getElementById('result');

        micBtn.onclick = async function() {
            if (!isRecording) {
                // Start recording
                audioChunks = [];
                stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                mediaRecorder.start();
                statusDiv.textContent = 'Recording...';
                micBtn.textContent = '⏹️ Stop';
                micBtn.classList.add('recording');
                isRecording = true;
                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };
                mediaRecorder.onstop = async () => {
                    statusDiv.textContent = 'Processing...';
                    micBtn.textContent = '🎤 Start';
                    micBtn.classList.remove('recording');
                    isRecording = false;
                    if (stream) {
                        stream.getTracks().forEach(track => track.stop());
                        stream = null;
                    }
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    const formData = new FormData();
                    formData.append('file', audioBlob, 'conversation.wav');
                    try {
                        const response = await fetch('http://localhost:8000/analyze-conversation/', {
                            method: 'POST',
                            body: formData
                        });
                        const data = await response.json();
                        if (response.ok) {
                            // Send the result to Streamlit via postMessage
                            window.parent.postMessage({type: 'doctor_patient_analysis', data: data}, '*');
                            // Also store in localStorage for fallback
                            localStorage.setItem('doctor_patient_analysis', JSON.stringify(data));
                            // Show transcript and timeline directly in the widget
                            let html = `<h2>📝 Full Transcript</h2><pre>${data.transcript || ''}</pre>`;
                            if (data.full_conversation && data.full_conversation.length > 0) {
                                html += `<h2>🗣️ Conversation Timeline</h2>`;
                                data.full_conversation.forEach(seg => {
                                    const speaker = seg.speaker ? seg.speaker.charAt(0).toUpperCase() + seg.speaker.slice(1) : 'Unknown';
                                    html += `<b>${speaker}:</b> ${seg.text}<br/>`;
                                });
                            }
                            resultDiv.innerHTML = html;
                        } else {
                            resultDiv.innerHTML = `<b style='color:red;'>Error:</b> ${data.error}`;
                        }
                        statusDiv.textContent = '';
                    } catch (err) {
                        resultDiv.innerHTML = `<b style='color:red;'>Error:</b> ${err}`;
                        statusDiv.textContent = '';
                    }
                };
            } else {
                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    mediaRecorder.stop();
                }
            }
        };
    </script>
</body>
</html>
'''

st.components.v1.html(html_code, height=800, scrolling=True)
