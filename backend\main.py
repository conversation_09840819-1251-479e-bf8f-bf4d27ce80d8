import os
import logging
from datetime import datetime
from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from groq_whisper import transcribe_audio
from pydub import AudioSegment
import json

# Setup simple logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

app = FastAPI()

# Allow CORS for local Streamlit frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

print("🚀 Medical Voice Chat Backend Started!")
print("📡 Server running on: http://localhost:8000")
print("📋 Available endpoints:")
print("   - GET  /                      : Health check")
print("   - POST /transcribe            : Simple transcription")
print("   - POST /analyze-conversation  : AI conversation analysis")
print("=" * 50)

@app.get("/")
def root():
    print("💓 [HEALTH] Health check requested")
    return {"message": "Voice-to-text API running", "status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/transcribe/")
async def transcribe(file: UploadFile = File(...)):
    print(f"📝 [TRANSCRIBE] Received file: {file.filename}")

    temp_path = f"temp_{file.filename}"
    with open(temp_path, "wb") as f:
        f.write(await file.read())

    print(f"💾 [TRANSCRIBE] File saved temporarily: {temp_path}")

    try:
        print("🎤 [TRANSCRIBE] Starting Groq Whisper transcription...")
        transcript = transcribe_audio(temp_path)
        print(f"✅ [TRANSCRIBE] Success! Transcript: {transcript[:100]}...")

        result = {"transcript": transcript}
        os.remove(temp_path)
        print("🗑️ [TRANSCRIBE] Temporary file cleaned up")
        return JSONResponse(result)
    except Exception as e:
        print(f"❌ [TRANSCRIBE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "transcript": "",
        }, status_code=500)

@app.post("/analyze-conversation/")
async def analyze_conversation(file: UploadFile = File(...)):
    print(f"🧠 [ANALYZE] Received conversation file: {file.filename}")

    temp_path = f"temp_conversation_{file.filename}"
    with open(temp_path, "wb") as f:
        f.write(await file.read())

    print(f"💾 [ANALYZE] File saved temporarily: {temp_path}")

    try:
        print("🎤 [ANALYZE] Starting Groq Whisper transcription...")
        full_transcript = transcribe_audio(temp_path)
        print(f"✅ [ANALYZE] Full transcript obtained: {full_transcript[:100]}...")

        print("🤖 [ANALYZE] Sending to LLM for speaker analysis...")
        analyzed_conversation = await analyze_speakers_with_llm(full_transcript)

        result = {
            "transcript": full_transcript,
            "doctor_transcript": analyzed_conversation.get("doctor_parts", ""),
            "patient_transcript": analyzed_conversation.get("patient_parts", ""),
            "full_conversation": analyzed_conversation.get("timeline", []),
            "analysis_confidence": analyzed_conversation.get("confidence", 0.8)
        }

        os.remove(temp_path)
        print("🗑️ [ANALYZE] Temporary file cleaned up")
        return JSONResponse(result)

    except Exception as e:
        print(f"❌ [ANALYZE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "transcript": "",
            "doctor_transcript": "",
            "patient_transcript": "",
            "full_conversation": []
        }, status_code=500)

async def analyze_speakers_with_llm(transcript):
    try:
        from groq import Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))

        prompt = f"""
You are an expert medical conversation analyst. Analyze this conversation transcript and identify and SEPARATE every doctor and patient turn. Do NOT mix both speakers in a single transcript block. Always separate doctor and patient lines, even if you are not 100% confident.

TRANSCRIPT:
{transcript}

ANALYSIS RULES:
1. If this appears to be a medical conversation with multiple speakers:
   - DOCTOR: Uses medical terminology, asks diagnostic questions, gives advice/treatment
   - PATIENT: Describes symptoms, asks questions, expresses concerns
2. If just one speaker, place everything in "doctor_parts"
3. Use context clues (questions, answers, medical terms) to infer speakers
4. Alternate speakers if unclear, start with patient if the first line is a symptom
5. NEVER assign two consecutive lines to the same speaker unless it is clearly a follow-up

Return output in this exact JSON format:
{{
    "doctor_parts": "...",
    "patient_parts": "...",
    "timeline": [
        {{"speaker": "doctor", "text": "...", "timestamp": "estimated"}},
        {{"speaker": "patient", "text": "...", "timestamp": "estimated"}}
    ],
    "confidence": 0.9
}}
"""

        print("🤖 [LLM] Sending conversation to Groq for analysis...")
        response = client.chat.completions.create(
            model="llama-3.1-8b-instant",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are a medical conversation analysis expert. "
                        "Always respond with valid JSON only. NEVER assign two consecutive lines to the same speaker unless it's clearly a follow-up."
                    )
                },
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )

        llm_response = response.choices[0].message.content.strip()
        print(f"✅ [LLM] Received analysis: {llm_response[:200]}...")

        try:
            analysis = json.loads(llm_response)
            print("✅ [LLM] Successfully parsed JSON response")

            # 🧹 Postprocess timeline to avoid repeated speaker
            cleaned_timeline = []
            last_speaker = None
            for i, item in enumerate(analysis.get("timeline", [])):
                speaker = item.get("speaker")
                text = item.get("text", "")

                if speaker == last_speaker and i > 0:
                    speaker = "patient" if last_speaker == "doctor" else "doctor"

                cleaned_timeline.append({
                    "speaker": speaker,
                    "text": text,
                    "timestamp": item.get("timestamp", "estimated")
                })
                last_speaker = speaker

            analysis["timeline"] = cleaned_timeline
            return analysis

        except json.JSONDecodeError:
            print("⚠️ [LLM] JSON parsing failed")
            return {
                "doctor_parts": "Analysis failed",
                "patient_parts": "Analysis failed",
                "timeline": [],
                "confidence": 0.3
            }

    except Exception as e:
        print(f"❌ [LLM] Analysis error: {e}")
        return {
            "doctor_parts": f"LLM Analysis Error: {str(e)}",
            "patient_parts": f"LLM Analysis Error: {str(e)}",
            "timeline": [],
            "confidence": 0.1
        }

@app.post("/summarize-conversation/")
async def summarize_conversation(data: dict):
    try:
        transcript = data.get("transcript", "")
        timeline = data.get("timeline", [])
        if timeline and isinstance(timeline, list):
            conversation_text = " ".join([seg.get("text", "") for seg in timeline])
        else:
            conversation_text = transcript

        from groq import Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))

        prompt = f"""
You are a medical conversation summarizer. Read the following conversation and provide a concise, clear summary of the main points, symptoms, diagnosis, and advice given. Use simple language.

CONVERSATION:
{conversation_text}

SUMMARY:
"""

        response = client.chat.completions.create(
            model="llama-3.1-8b-instant",
            messages=[
                {"role": "system", "content": "You are a medical conversation summarizer. Always respond with a clear summary only."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=400
        )

        summary = response.choices[0].message.content.strip()
        return {"summary": summary}
    except Exception as e:
        return {"error": str(e), "summary": ""}
